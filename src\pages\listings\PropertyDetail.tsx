import { useParams } from "react-router-dom";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import Reviews from "@/components/properties/Reviews";
import PropertyHeader from "@/components/properties/PropertyHeader";
import PropertyGallery from "@/components/properties/PropertyGallery";
import PropertyAmenities from "@/components/properties/PropertyAmenities";
import PropertyBookingWidget from "@/components/properties/PropertyBookingWidget";
import PropertyDetailSkeleton from "@/components/properties/PropertyDetailSkeleton";
import PropertyDetailError from "@/components/properties/PropertyDetailError";
import { usePropertyDetails } from "@/hooks/usePropertyDetails";
import { Bed, Bath } from "lucide-react";
import { useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useSearchParams, useNavigate } from "react-router-dom";
import { MapDisplay } from "@/components/maps/MapDisplay";

const PropertyDetail = () => {
  const { id } = useParams();
  const { data: property, isLoading, error } = usePropertyDetails(id);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();

  const sessionId = searchParams.get("session_id");
  const bookingId = searchParams.get("booking_id");
  const isSuccess = window.location.pathname.includes("payment-success");
  const isCancelled = window.location.pathname.includes("payment-cancelled");

  useEffect(() => {
    if (sessionId && bookingId && isSuccess) {
      // Verify the payment
      const verifyPayment = async () => {
        try {
          const { data, error } = await supabase.functions.invoke(
            "verify-stripe-payment",
            {
              body: { sessionId, bookingId },
            }
          );

          if (error) throw error;

          if (data.paymentStatus === "completed") {
            toast({
              title: "Payment successful",
              description: "Your booking has been confirmed!",
            });
          } else {
            toast({
              title: "Payment processing",
              description:
                "Your payment is being processed. We'll notify you when it's complete.",
            });
          }

          // Remove query params from URL
          navigate(`/listings/${id}`, { replace: true });
        } catch (error: any) {
          console.error("Payment verification error:", error);
          toast({
            title: "Payment verification failed",
            description: error.message,
            variant: "destructive",
          });
        }
      };

      verifyPayment();
    }

    if (isCancelled && bookingId) {
      toast({
        title: "Payment cancelled",
        description:
          "Your booking has not been completed. You can try again later.",
        variant: "destructive",
      });

      // Remove query params from URL
      navigate(`/listings/${id}`, { replace: true });
    }
  }, [sessionId, bookingId, isSuccess, isCancelled, id, navigate, toast]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <PropertyDetailSkeleton />
      </div>
    );
  }

  if (error || !property) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <PropertyDetailError />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <main className="container mx-auto px-4 py-8">
        <PropertyHeader
          title={property.title}
          location={property.location}
          rating={property.rating}
          reviews={property.reviews}
          isSuperHost={property.isSuperHost}
          is_dummy={property.is_dummy}
        />

        <PropertyGallery images={property.images} title={property.title} />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <h2 className="text-2xl font-bold mb-4">About {property.title}</h2>

              <div className="flex items-center mb-4 text-gray-700">
                <Bed className="w-5 h-5 mr-2" />
                <span>
                  {property.beds} {property.beds === 1 ? "bed" : "beds"}
                </span>
                <span className="mx-3">·</span>
                <Bath className="w-5 h-5 mr-2" />
                <span>
                  {property.baths} {property.baths === 1 ? "bath" : "baths"}
                </span>
              </div>

              <p className="text-gray-700 whitespace-pre-line">
                {property.description}
              </p>
            </div>

            <PropertyAmenities features={property.features} />

            {/* Location Map */}
            {property.latitude && property.longitude && (
              <div className="mt-6">
                <MapDisplay
                  location={{
                    latitude: property.latitude,
                    longitude: property.longitude,
                    formatted_address:
                      property.formatted_address || property.location,
                  }}
                  title={property.title}
                  height="350px"
                  showNavigationButtons={true}
                  showInfoWindow={true}
                />
              </div>
            )}

            <div className="bg-white rounded-lg shadow-sm p-6 mt-6">
              <Reviews propertyId={id} />
            </div>
          </div>

          <div className="lg:col-span-1">
            <PropertyBookingWidget
              price={property.price}
              rating={property.rating}
              propertyId={property.id}
              propertyTitle={property.title}
              hostId={property.owner?.id}
              isDummy={property.is_dummy}
            />
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default PropertyDetail;
