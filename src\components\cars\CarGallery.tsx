import { useState } from "react";
import { Button } from "@/components/ui/button";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { ChevronLeft, ChevronRight, X, Grid3X3 } from "lucide-react";

interface CarGalleryProps {
  images: string[];
  title: string;
}

const CarGallery = ({ images, title }: CarGalleryProps) => {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isFullScreenOpen, setIsFullScreenOpen] = useState(false);
  const [mainImage, setMainImage] = useState(images[0] || "/placeholder.svg");

  const nextImage = () => {
    setSelectedImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setSelectedImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const handleImageClick = (image: string, index: number) => {
    setMainImage(image);
    setSelectedImageIndex(index);
    setIsModalOpen(true);
  };

  if (images.length === 0) {
    return (
      <div className="space-y-4">
        <div className="overflow-hidden rounded-lg border">
          <AspectRatio
            ratio={16 / 9}
            className="bg-muted flex items-center justify-center"
          >
            <img
              src="/placeholder.svg"
              alt="No image available"
              className="w-32 h-32 opacity-50"
            />
          </AspectRatio>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Main Image */}
      <div className="overflow-hidden rounded-lg border cursor-pointer group">
        <AspectRatio ratio={16 / 9} className="bg-muted">
          <img
            src={mainImage}
            alt={`${title} - Main view`}
            className="object-cover w-full h-full transition-transform group-hover:scale-105"
            onClick={() =>
              handleImageClick(mainImage, images.indexOf(mainImage))
            }
          />
        </AspectRatio>
      </div>

      {/* Thumbnails Grid - Show more thumbnails in a better layout */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">
            {images.length} photo{images.length !== 1 ? "s" : ""}
          </span>
          {images.length > 6 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsModalOpen(true)}
              className="flex items-center gap-2"
            >
              <Grid3X3 className="h-4 w-4" />
              View All
            </Button>
          )}
        </div>

        {/* Thumbnail Grid */}
        <div className="grid grid-cols-6 gap-2">
          {images.slice(0, 6).map((image, index) => (
            <Button
              key={index}
              variant="ghost"
              className={`p-0 overflow-hidden rounded-md aspect-square border border-gray-200 hover:border-accent transition-all duration-150 ${
                image === mainImage ? "ring-2 ring-accent border-accent" : ""
              }`}
              onClick={() => setMainImage(image)}
              aria-label={`Show image ${index + 1}`}
            >
              <img
                src={image}
                alt={`${title} - View ${index + 1}`}
                className="object-cover w-full h-full"
              />
              {/* Show overlay on last thumbnail if there are more images */}
              {index === 5 && images.length > 6 && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <span className="text-white text-xs font-medium">
                    +{images.length - 6}
                  </span>
                </div>
              )}
            </Button>
          ))}
        </div>
      </div>

      {/* Image Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-5xl w-[90vw] h-[80vh] p-0 flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b bg-white z-10 flex-shrink-0">
            <h3 className="text-lg font-semibold truncate">{title}</h3>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                {images.length} images
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsModalOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Images Grid */}
          <div className="flex-1 overflow-y-auto p-4 min-h-0">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3 md:gap-4">
                {images.map((image, index) => (
                  <div
                    key={index}
                    className="aspect-square rounded-lg overflow-hidden bg-gray-100 hover:shadow-lg transition-shadow cursor-pointer"
                    onClick={() => {
                      setSelectedImageIndex(index);
                      setIsFullScreenOpen(true);
                    }}
                  >
                    <img
                      src={image}
                      alt={`${title} - ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
            </div>
        </DialogContent>
      </Dialog>

      {/* Full-Screen Image Modal */}
      <Dialog open={isFullScreenOpen} onOpenChange={setIsFullScreenOpen}>
        <DialogContent className="max-w-none w-screen h-screen p-0 bg-black/95 flex items-center justify-center">
          <div className="relative w-full h-full flex items-center justify-center">
            {/* Close Button */}
            <Button
              variant="ghost"
              size="sm"
              className="absolute top-4 right-4 z-50 text-white hover:bg-white/20"
              onClick={() => setIsFullScreenOpen(false)}
            >
              <X className="h-6 w-6" />
            </Button>

            {/* Navigation Buttons */}
            {images.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute left-4 top-1/2 -translate-y-1/2 z-50 text-white hover:bg-white/20"
                  onClick={prevImage}
                >
                  <ChevronLeft className="h-8 w-8" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-4 top-1/2 -translate-y-1/2 z-50 text-white hover:bg-white/20"
                  onClick={nextImage}
                >
                  <ChevronRight className="h-8 w-8" />
                </Button>
              </>
            )}

            {/* Image */}
            <img
              src={images[selectedImageIndex]}
              alt={`${title} - ${selectedImageIndex + 1}`}
              className="max-w-full max-h-full object-contain"
            />

            {/* Image Counter */}
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
              {selectedImageIndex + 1} / {images.length}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CarGallery;
