import { useState } from "react";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { ChevronLeft, ChevronRight, X, Grid3X3 } from "lucide-react";

interface PropertyGalleryProps {
  images: string[];
  title: string;
}

const PropertyGallery = ({ images, title }: PropertyGalleryProps) => {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isFullScreenOpen, setIsFullScreenOpen] = useState(false);

  const nextImage = () => {
    setSelectedImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setSelectedImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  if (images.length === 0) {
    return (
      <div className="aspect-[4/3] rounded-lg bg-gray-200 flex items-center justify-center mb-8">
        <img
          src="/placeholder.svg"
          alt="No image available"
          className="w-32 h-32 opacity-50"
        />
      </div>
    );
  }

  return (
    <div className="mb-8">
      {/* Main Gallery Grid */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Main Image */}
        <div className="md:col-span-2 aspect-[4/3] rounded-lg overflow-hidden relative group cursor-pointer">
          <img
            src={images[0]}
            alt={title}
            className="w-full h-full object-cover transition-transform group-hover:scale-105"
            onClick={() => {
              setSelectedImageIndex(0);
              setIsModalOpen(true);
            }}
          />
        </div>

        {/* Side Images Grid */}
        <div className="md:col-span-2 grid grid-cols-2 gap-4">
          {images.slice(1, 5).map((image, index) => (
            <div
              key={index}
              className="aspect-square rounded-lg overflow-hidden relative group cursor-pointer"
              onClick={() => {
                setSelectedImageIndex(index + 1);
                setIsModalOpen(true);
              }}
            >
              <img
                src={image}
                alt={`${title} - ${index + 2}`}
                className="w-full h-full object-cover transition-transform group-hover:scale-105"
              />
              {/* Show "View All" overlay on last visible image if there are more */}
              {index === 3 && images.length > 5 && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <div className="text-white text-center">
                    <Grid3X3 className="h-6 w-6 mx-auto mb-1" />
                    <span className="text-sm font-medium">
                      +{images.length - 5} more
                    </span>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* View All Photos Button */}
      {images.length > 5 && (
        <div className="mt-4">
          <Button
            variant="outline"
            onClick={() => setIsModalOpen(true)}
            className="flex items-center gap-2"
          >
            <Grid3X3 className="h-4 w-4" />
            View All {images.length} Photos
          </Button>
        </div>
      )}

      {/* Image Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-5xl w-[90vw] h-[80vh] p-0 flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b bg-white z-10 flex-shrink-0">
            <h3 className="text-lg font-semibold truncate">{title}</h3>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                {images.length} images
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsModalOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Images Grid */}
          <div className="flex-1 overflow-y-auto p-4 min-h-0">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3 md:gap-4">
                {images.map((image, index) => (
                  <div
                    key={index}
                    className="aspect-square rounded-lg overflow-hidden bg-gray-100 hover:shadow-lg transition-shadow cursor-pointer"
                    onClick={() => {
                      setSelectedImageIndex(index);
                      setIsFullScreenOpen(true);
                    }}
                  >
                    <img
                      src={image}
                      alt={`${title} - ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
            </div>
        </DialogContent>
      </Dialog>

      {/* Full-Screen Image Modal */}
      <Dialog open={isFullScreenOpen} onOpenChange={setIsFullScreenOpen}>
        <DialogContent className="max-w-none w-screen h-screen p-0 bg-black/95 flex items-center justify-center">
          <div className="relative w-full h-full flex items-center justify-center">
            {/* Close Button */}
            <Button
              variant="ghost"
              size="sm"
              className="absolute top-4 right-4 z-50 text-white hover:bg-white/20"
              onClick={() => setIsFullScreenOpen(false)}
            >
              <X className="h-6 w-6" />
            </Button>

            {/* Navigation Buttons */}
            {images.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute left-4 top-1/2 -translate-y-1/2 z-50 text-white hover:bg-white/20"
                  onClick={prevImage}
                >
                  <ChevronLeft className="h-8 w-8" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-4 top-1/2 -translate-y-1/2 z-50 text-white hover:bg-white/20"
                  onClick={nextImage}
                >
                  <ChevronRight className="h-8 w-8" />
                </Button>
              </>
            )}

            {/* Image */}
            <img
              src={images[selectedImageIndex]}
              alt={`${title} - ${selectedImageIndex + 1}`}
              className="max-w-full max-h-full object-contain"
            />

            {/* Image Counter */}
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
              {selectedImageIndex + 1} / {images.length}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PropertyGallery;
