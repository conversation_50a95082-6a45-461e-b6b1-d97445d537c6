import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// African currencies that we support
const AFRICAN_CURRENCIES = [
  'USD', 'NGN', 'ZAR', 'KES', 'GHS', 'EGP', 'MAD', 'TND', 'ETB', 'UGX',
  'TZS', 'RWF', 'XOF', 'XAF', 'MZN', 'BWP', 'NAD', 'SZL', 'LSL', 'MWK',
  'ZMW', 'AOA', 'DZD', 'LYD', 'SDG', 'SOS', 'DJF', 'ERN', 'MGA', 'MUR',
  'SCR', 'KMF', 'CVE', 'STP', 'GMD', 'GNF', 'LRD', 'SLL', 'BIF', 'CDF'
];

interface ExchangeRateResponse {
  base_code: string;
  base_name: string;
  base_amount: number;
  last_updated: string;
  rates: Array<{
    code: string;
    name: string;
    rate: number;
  }>;
}

serve(async (req) => {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 });
    }

    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const forexApiKey = Deno.env.get('FOREX_API_KEY');

    if (!forexApiKey) {
      console.error('FOREX_API_KEY environment variable is not set');
      return new Response('API key not configured', { status: 500 });
    }

    // Create Supabase client
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    console.log('Fetching exchange rates from SilverLining API...');

    // Fetch exchange rates from SilverLining API
    const response = await fetch('https://forex-aws.silverlining.cloud/rates', {
      method: 'POST',
      headers: {
        'x-api-key': forexApiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        base: 'USD',
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: ExchangeRateResponse = await response.json();
    console.log(`Received ${data.rates.length} exchange rates`);

    // Filter for African currencies only
    const africanRates = data.rates.filter(rate => 
      AFRICAN_CURRENCIES.includes(rate.code)
    );

    console.log(`Filtered to ${africanRates.length} African currencies`);

    // Update rates in database
    let updatedCount = 0;
    let errorCount = 0;

    for (const rate of africanRates) {
      try {
        const { error } = await supabase
          .from('currency_rates')
          .upsert({
            base_currency: 'USD',
            target_currency: rate.code,
            rate: rate.rate,
            last_updated: new Date().toISOString(),
          }, {
            onConflict: 'base_currency,target_currency'
          });

        if (error) {
          console.error(`Error updating rate for ${rate.code}:`, error);
          errorCount++;
        } else {
          updatedCount++;
        }
      } catch (err) {
        console.error(`Exception updating rate for ${rate.code}:`, err);
        errorCount++;
      }
    }

    console.log(`Successfully updated ${updatedCount} rates, ${errorCount} errors`);

    return new Response(
      JSON.stringify({
        success: true,
        message: `Updated ${updatedCount} currency rates`,
        updated_count: updatedCount,
        error_count: errorCount,
        last_updated: new Date().toISOString(),
      }),
      {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      }
    );

  } catch (error) {
    console.error('Error updating currency rates:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      }),
      {
        headers: { 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});
